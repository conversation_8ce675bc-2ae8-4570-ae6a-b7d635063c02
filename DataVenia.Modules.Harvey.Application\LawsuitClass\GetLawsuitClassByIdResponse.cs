﻿namespace DataVenia.Modules.Harvey.Application.LawsuitClass;

public sealed record GetLawsuitClassByIdResponse(
    int Id,
    string Type,
    string LegalProvision,
    string Article,
    string Acronym,
    string OldAcronym,
    string ActivePole,
    string PassivePole,
    string Glossary,
    bool IsOwnNumbering,
    bool IsFirstInstance,
    bool IsSecondInstance,
    bool JustEsJuizadoEs,
    bool JustEsTurmas,
    bool JustEs1grauMil,
    bool JustEs2grauMil,
    bool JustEsJuizadoEsFp,
    bool JustTuEsUn,
    bool JustFed1grau,
    bool JustFed2grau,
    bool JustFedJuizadoEs,
    bool JustFedTurmas,
    bool JustFedNacional,
    bool JustFedRegional,
    bool JustTrab1grau,
    bool JustTrab2grau,
    bool JustTrabTst,
    bool JustTrabCsjt,
    bool Stf,
    bool Stj,
    bool Cjf,
    bool Cnj,
    bool JustMilUniao1grau,
    bool JustMilUniaoStm,
    bool JustMilEst1grau,
    bool JustMilEstTjm,
    bool JustElei1grau,
    bool JustElei2grau,
    bool JustEleiTse,
    string ItemType,
    string? IncludedBy,
    DateTime? IncludedAt,
    bool IsCriminal);

using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddClassesAndTopicsData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert lawsuit classes data
            migrationBuilder.Sql(@"
                INSERT INTO harvey.lawsuit_class (
                    id, type, legal_provision, article, acronym, old_acronym, active_pole, passive_pole,
                    glossary, is_own_numbering, is_first_instance, is_second_instance, just_es_juizado_es,
                    just_es_turmas, just_es_1grau_mil, just_es_2grau_mil, just_es_juizado_es_fp, just_tu_es_un,
                    just_fed_1grau, just_fed_2grau, just_fed_juizado_es, just_fed_turmas, just_fed_nacional,
                    just_fed_regional, just_trab_1grau, just_trab_2grau, just_trab_tst, just_trab_csjt,
                    stf, stj, cjf, cnj, just_mil_uniao_1grau, just_mil_uniao_stm, just_mil_est_1grau,
                    just_mil_est_tjm, just_elei_1grau, just_elei_2grau, just_elei_tse, item_type,
                    included_by, included_at, user_ip, updated_by, procedure_id, procedure_origin, is_criminal
                ) VALUES
                (7, 'Conhecimento', 'CPC 2015', '318', 'ProceComCiv', '', 'Autor', 'Réu', '<div class=''cnj-content''><p> Art. 318. Aplica-se a todas as causas o procedimento comum, salvo disposicao em contrario deste Codigo ou de lei.</p> <p> Paragrafo unico. O procedimento comum aplica-se subsidiariamente aos demais procedimentos especiais e ao processo de execucao.</p></div>', true, true, true, false, false, true, false, false, false, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, false, false, false, false, 'C', null, null, null, 'raquel.souza', null, null, false),
                (22, '', 'CPC', '275', 'ProSum', '', 'Autor ', 'Réu', '<div class=''cnj-content''>Art. 275. Observar-se-a o procedimento sumario: (Redacao dada pela Lei no 9.245, de 26.12.1995) I - nas causas cujo valor nao exceda a 60 (sessenta) vezes o valor do salario minimo; (Redacao dada pela Lei no 10.444, de 7.5.2002) II - nas causas, qualquer que seja o valor (Redacao dada pela Lei no 9.245, de 26.12.1995) a) de arrendamento rural e de parceria agricola; (Redacao dada pela Lei no 9.245, de 26.12.1995) b) de cobranca ao condomino de quaisquer quantias devidas ao condominio; (Redacao dada pela Lei no 9.245, de 26.12.1995) c) de ressarcimento por danos em predio urbano ou rustico; (Redacao dada pela Lei no 9.245, de 26.12.1995) d) de ressarcimento por danos causados em acidente de veiculo de via terrestre; (Redacao dada pela Lei no 9.245, de 26.12.1995) e) de cobranca de seguro, relativamente aos danos causados em acidente de veiculo, ressalvados os casos de processo de execucao; (Redacao dada pela Lei no 9.245, de 26.12.1995) f) de cobranca de honorarios dos profissionais liberais, ressalvado o disposto em legislacao especial; (Redacao dada pela Lei no 9.245, de 26.12.1995) g) nos demais casos previstos em lei. (Redacao dada pela Lei no 9.245, de 26.12.1995) Paragrafo unico. Este procedimento nao sera observado nas acoes relativas ao estado e a capacidade das pessoas. (Redacao dada pela Lei no 9.245, de 26.12.1995)</div>', true, true, false, false, false, true, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, false, false, false, false, 'C', null, null, null, '9', null, null, false),
                (28, 'Especial', 'CPC', '907', 'ASTP', '', 'Autor', 'Réu', '<div class=''cnj-content''><p> Art. 907. Aquele que tiver perdido titulo ao portador ou dele houver sido injustamente desapossado podera: I - reivindica-lo da pessoa que o detiver; II - requerer-lhe a anulacao e substituicao por outro.</p></div>', true, true, false, false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 'C', null, null, null, 'pedro.amorim', null, null, false),
                (29, 'Especial', 'CPC', '1071', 'ADCVRD', '', 'Autor', 'Réu', '<div class=''cnj-content''>Art. 1.071. Ocorrendo mora do comprador, provada com o protesto do titulo, o vendedor podera requerer, liminarmente e sem audiencia do comprador, a apreensao e deposito da coisa vendida. § 1o Ao deferir o pedido, nomeara o juiz perito, que procedera a vistoria da coisa e arbitramento do seu valor, descrevendo-lhe o estado e individuando-a com todos os caracteristicos. § 2o Feito o deposito, sera citado o comprador para, dentro em 5 (cinco) dias, contestar a acao. Neste prazo podera o comprador, que houver pago mais de 40% (quarenta por cento) do preco, requerer ao juiz que Ihe conceda 30 (trinta) dias para reaver a coisa, liquidando as prestacoes vencidas, juros, honorarios e custas. § 3o Se o reu nao contestar, deixar de pedir a concessao do prazo ou nao efetuar o pagamento referido no paragrafo anterior, podera o autor, mediante a apresentacao dos titulos vencidos e vincendos, requerer a reintegracao imediata na posse da coisa depositada; caso em que, descontada do valor arbitrado a importancia da divida acrescida das despesas judiciais e extrajudiciais, o autor restituira ao reu o saldo, depositando-o em pagamento. § 4o Se a acao for contestada, observar-se-a o procedimento ordinario, sem prejuizo da reintegracao liminar.</div>', true, true, false, false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, 'C', null, null, null, null, null, null, false);")

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}

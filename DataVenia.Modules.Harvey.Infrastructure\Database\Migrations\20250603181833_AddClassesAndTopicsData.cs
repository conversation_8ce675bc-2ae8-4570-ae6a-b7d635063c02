﻿using Microsoft.EntityFrameworkCore.Migrations;
using System.Text.Json;

#nullable disable

namespace DataVenia.Modules.Harvey.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddClassesAndTopicsData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert lawsuit classes data from JSON
            var classesJsonPath =
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "Data", "classes.json");
            if (!File.Exists(classesJsonPath))
            {
                // Try alternative path for development environment
                classesJsonPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..",
                    "DataVenia.Modules.Harvey.Infrastructure", "Database", "Data", "classes.json"));
            }

            if (File.Exists(classesJsonPath))
            {
                var classesJson = File.ReadAllText(classesJsonPath);
                var classes = JsonSerializer.Deserialize<Domain.LawsuitClass.LawsuitClass[]>(classesJson);

                if (classes != null)
                {
                    foreach (var lawsuitClass in classes)
                    {
                        migrationBuilder.Sql($@"
                            INSERT INTO harvey.lawsuit_class (
                                id, type, legal_provision, article, acronym, old_acronym, active_pole, passive_pole,
                                glossary, is_own_numbering, is_first_instance, is_second_instance, just_es_juizado_es,
                                just_es_turmas, just_es_1grau_mil, just_es_2grau_mil, just_es_juizado_es_fp, just_tu_es_un,
                                just_fed_1grau, just_fed_2grau, just_fed_juizado_es, just_fed_turmas, just_fed_nacional,
                                just_fed_regional, just_trab_1grau, just_trab_2grau, just_trab_tst, just_trab_csjt,
                                stf, stj, cjf, cnj, just_mil_uniao_1grau, just_mil_uniao_stm, just_mil_est_1grau,
                                just_mil_est_tjm, just_elei_1grau, just_elei_2grau, just_elei_tse, item_type,
                                included_by, included_at, user_ip, updated_by, procedure_id, procedure_origin, is_criminal
                            ) VALUES (
                                {lawsuitClass.Id},
                                '{lawsuitClass.Type?.Replace("'", "''")}',
                                '{lawsuitClass.LegalProvision?.Replace("'", "''")}',
                                '{lawsuitClass.Article?.Replace("'", "''")}',
                                '{lawsuitClass.Acronym?.Replace("'", "''")}',
                                '{lawsuitClass.OldAcronym?.Replace("'", "''")}',
                                '{lawsuitClass.ActivePole?.Replace("'", "''")}',
                                '{lawsuitClass.PassivePole?.Replace("'", "''")}',
                                '{lawsuitClass.Glossary?.Replace("'", "''")}',
                                {lawsuitClass.IsOwnNumbering.ToString().ToLower()},
                                {lawsuitClass.IsFirstInstance.ToString().ToLower()},
                                {lawsuitClass.IsSecondInstance.ToString().ToLower()},
                                {lawsuitClass.JustEsJuizadoEs.ToString().ToLower()},
                                {lawsuitClass.JustEsTurmas.ToString().ToLower()},
                                {lawsuitClass.JustEs1grauMil.ToString().ToLower()},
                                {lawsuitClass.JustEs2grauMil.ToString().ToLower()},
                                {lawsuitClass.JustEsJuizadoEsFp.ToString().ToLower()},
                                {lawsuitClass.JustTuEsUn.ToString().ToLower()},
                                {lawsuitClass.JustFed1grau.ToString().ToLower()},
                                {lawsuitClass.JustFed2grau.ToString().ToLower()},
                                {lawsuitClass.JustFedJuizadoEs.ToString().ToLower()},
                                {lawsuitClass.JustFedTurmas.ToString().ToLower()},
                                {lawsuitClass.JustFedNacional.ToString().ToLower()},
                                {lawsuitClass.JustFedRegional.ToString().ToLower()},
                                {lawsuitClass.JustTrab1grau.ToString().ToLower()},
                                {lawsuitClass.JustTrab2grau.ToString().ToLower()},
                                {lawsuitClass.JustTrabTst.ToString().ToLower()},
                                {lawsuitClass.JustTrabCsjt.ToString().ToLower()},
                                {lawsuitClass.Stf.ToString().ToLower()},
                                {lawsuitClass.Stj.ToString().ToLower()},
                                {lawsuitClass.Cjf.ToString().ToLower()},
                                {lawsuitClass.Cnj.ToString().ToLower()},
                                {lawsuitClass.JustMilUniao1grau.ToString().ToLower()},
                                {lawsuitClass.JustMilUniaoStm.ToString().ToLower()},
                                {lawsuitClass.JustMilEst1grau.ToString().ToLower()},
                                {lawsuitClass.JustMilEstTjm.ToString().ToLower()},
                                {lawsuitClass.JustElei1grau.ToString().ToLower()},
                                {lawsuitClass.JustElei2grau.ToString().ToLower()},
                                {lawsuitClass.JustEleiTse.ToString().ToLower()},
                                '{lawsuitClass.ItemType?.Replace("'", "''")}',
                                {(lawsuitClass.IncludedBy != null ? $"'{lawsuitClass.IncludedBy.Replace("'", "''")}'" : "null")},
                                {(lawsuitClass.IncludedAt != null ? $"'{lawsuitClass.IncludedAt:yyyy-MM-dd HH:mm:ss}'" : "null")},
                                {(lawsuitClass.UserIp != null ? $"'{lawsuitClass.UserIp.Replace("'", "''")}'" : "null")},
                                {(lawsuitClass.UpdatedBy != null ? $"'{lawsuitClass.UpdatedBy.Replace("'", "''")}'" : "null")},
                                {(lawsuitClass.ProcedureId != null ? $"'{lawsuitClass.ProcedureId.Replace("'", "''")}'" : "null")},
                                {(lawsuitClass.ProcedureOrigin != null ? $"'{lawsuitClass.ProcedureOrigin.Replace("'", "''")}'" : "null")},
                                {(lawsuitClass.IsCriminal.ToString().ToLower() ?? "false")}
                            ) ON CONFLICT (id) DO NOTHING;");
                    }
                }
            }

            // Insert lawsuit topics data from JSON
            var topicsJsonPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "Data", "topics.json");
            if (!File.Exists(topicsJsonPath))
            {
                // Try alternative path for development environment
                topicsJsonPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..",
                    "DataVenia.Modules.Harvey.Infrastructure", "Database", "Data", "topics.json"));
            }

            if (File.Exists(topicsJsonPath))
            {
                var topicsJson = File.ReadAllText(topicsJsonPath);
                var topics = JsonSerializer.Deserialize<Domain.LawsuitTopic.LawsuitTopic[]>(topicsJson);

                if (topics != null)
                {
                    foreach (var topic in topics)
                    {
                        migrationBuilder.Sql($@"
                            INSERT INTO harvey.lawsuit_topic (
                                id, legal_provision, article, glossary, is_secret, secondary_topic, previous_crime,
                                is_first_instance, is_second_instance, just_es_juizado_es, just_es_turmas,
                                just_es_1grau_mil, just_es_2grau_mil, just_es_juizado_es_fp, just_tu_es_un,
                                just_fed_1grau, just_fed_2grau, just_fed_juizado_es, just_fed_turmas,
                                just_fed_nacional, just_fed_regional, just_trab_1grau, just_trab_2grau,
                                just_trab_tst, just_trab_csjt, stf, stj, cjf, cnj, just_mil_uniao_1grau,
                                just_mil_uniao_stm, just_mil_est_1grau, just_mil_est_tjm, just_elei_1grau,
                                just_elei_2grau, just_elei_tse, tipo_item, updated_by, updated_at, user_ip, updated_by_id
                            ) VALUES (
                                {topic.Id},
                                '{topic.LegalProvision?.Replace("'", "''")}',
                                '{topic.Article?.Replace("'", "''")}',
                                '{topic.Glossary?.Replace("'", "''")}',
                                {topic.IsSecret.ToString().ToLower()},
                                {topic.SecondaryTopic.ToString().ToLower()},
                                {topic.PreviousCrime.ToString().ToLower()},
                                {topic.IsFirstInstance.ToString().ToLower()},
                                {topic.IsSecondInstance.ToString().ToLower()},
                                {topic.JustEsJuizadoEs.ToString().ToLower()},
                                {topic.JustEsTurmas.ToString().ToLower()},
                                {topic.JustEs1grauMil.ToString().ToLower()},
                                {topic.JustEs2grauMil.ToString().ToLower()},
                                {topic.JustEsJuizadoEsFp.ToString().ToLower()},
                                {topic.JustTuEsUn.ToString().ToLower()},
                                {topic.JustFed1grau.ToString().ToLower()},
                                {topic.JustFed2grau.ToString().ToLower()},
                                {topic.JustFedJuizadoEs.ToString().ToLower()},
                                {topic.JustFedTurmas.ToString().ToLower()},
                                {topic.JustFedNacional.ToString().ToLower()},
                                {topic.JustFedRegional.ToString().ToLower()},
                                {topic.JustTrab1grau.ToString().ToLower()},
                                {topic.JustTrab2grau.ToString().ToLower()},
                                {topic.JustTrabTst.ToString().ToLower()},
                                {topic.JustTrabCsjt.ToString().ToLower()},
                                {topic.Stf.ToString().ToLower()},
                                {topic.Stj.ToString().ToLower()},
                                {topic.Cjf.ToString().ToLower()},
                                {topic.Cnj.ToString().ToLower()},
                                {topic.JustMilUniao1grau.ToString().ToLower()},
                                {topic.JustMilUniaoStm.ToString().ToLower()},
                                {topic.JustMilEst1grau.ToString().ToLower()},
                                {topic.JustMilEstTjm.ToString().ToLower()},
                                {topic.JustElei1grau.ToString().ToLower()},
                                {topic.JustElei2grau.ToString().ToLower()},
                                {topic.JustEleiTse.ToString().ToLower()},
                                {(topic.UpdatedBy != null ? $"'{topic.UpdatedBy.Replace("'", "''")}'" : "null")},
                                {(topic.UpdatedAt != null ? $"'{topic.UpdatedAt:yyyy-MM-dd HH:mm:ss}'" : "null")},
                                {(topic.UserIp != null ? $"'{topic.UserIp.Replace("'", "''")}'" : "null")},
                                {(topic.UpdatedById != null ? $"'{topic.UpdatedById.Replace("'", "''")}'" : "null")}
                            ) ON CONFLICT (id) DO NOTHING;");
                    }
                }
            }
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM harvey.lawsuit_class;");
            migrationBuilder.Sql("DELETE FROM harvey.lawsuit_topic;");
        }
    }
}

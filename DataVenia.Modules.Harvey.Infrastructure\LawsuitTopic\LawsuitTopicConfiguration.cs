﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataVenia.Modules.Harvey.Infrastructure.LawsuitTopic;

internal sealed class LawsuitTopicConfiguration : IEntityTypeConfiguration<Domain.LawsuitTopic.LawsuitTopic>
{
    public void Configure(EntityTypeBuilder<Domain.LawsuitTopic.LawsuitTopic> builder)
    {
        // Table Mapping
        builder.ToTable("lawsuit_topic");

        // Primary Key
        builder.HasKey(t => t.Id);

        // Properties
        builder.Property(t => t.LegalProvision)
            .HasMaxLength(255);

        builder.Property(t => t.Article)
            .HasMaxLength(255);

        builder.Property(t => t.Glossary)
            .HasColumnType("text");

        builder.Property(t => t.IsSecret)
            .IsRequired();

        builder.Property(t => t.SecondaryTopic)
            .IsRequired();

        builder.Property(t => t.PreviousCrime)
            .IsRequired();

        builder.Property(t => t.IsFirstInstance)
            .IsRequired();

        builder.Property(t => t.IsSecondInstance)
            .IsRequired();

        builder.Property(t => t.JustEsJuizadoEs)
            .IsRequired();

        builder.Property(t => t.JustEsTurmas)
            .IsRequired();

        builder.Property(t => t.JustEs1grauMil)
            .IsRequired();

        builder.Property(t => t.JustEs2grauMil)
            .IsRequired();

        builder.Property(t => t.JustEsJuizadoEsFp)
            .IsRequired();

        builder.Property(t => t.JustTuEsUn)
            .IsRequired();

        builder.Property(t => t.JustFed1grau)
            .IsRequired();

        builder.Property(t => t.JustFed2grau)
            .IsRequired();

        builder.Property(t => t.JustFedJuizadoEs)
            .IsRequired();

        builder.Property(t => t.JustFedTurmas)
            .IsRequired();

        builder.Property(t => t.JustFedNacional)
            .IsRequired();

        builder.Property(t => t.JustFedRegional)
            .IsRequired();

        builder.Property(t => t.JustTrab1grau)
            .IsRequired();

        builder.Property(t => t.JustTrab2grau)
            .IsRequired();

        builder.Property(t => t.JustTrabTst)
            .IsRequired();

        builder.Property(t => t.JustTrabCsjt)
            .IsRequired();

        builder.Property(t => t.Stf)
            .IsRequired();

        builder.Property(t => t.Stj)
            .IsRequired();

        builder.Property(t => t.Cjf)
            .IsRequired();

        builder.Property(t => t.Cnj)
            .IsRequired();

        builder.Property(t => t.JustMilUniao1grau)
            .IsRequired();

        builder.Property(t => t.JustMilUniaoStm)
            .IsRequired();

        builder.Property(t => t.JustMilEst1grau)
            .IsRequired();

        builder.Property(t => t.JustMilEstTjm)
            .IsRequired();

        builder.Property(t => t.JustElei1grau)
            .IsRequired();

        builder.Property(t => t.JustElei2grau)
            .IsRequired();

        builder.Property(t => t.JustEleiTse)
            .IsRequired();

        builder.Property(t => t.ItemType)
            .HasMaxLength(1);

        builder.Property(t => t.UpdatedBy)
            .HasMaxLength(255);

        builder.Property(t => t.UserIp)
            .HasMaxLength(50);

        builder.Property(t => t.UpdatedById)
            .HasMaxLength(255);

        builder.Property(t => t.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    /// <inheritdoc />
    public partial class ChangeAnalyzedByFieldToGuid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "analyzed_by",
                schema: "lawsuit",
                table: "data_divergence",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "analyzed_by",
                schema: "lawsuit",
                table: "data_divergence",
                type: "text",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);
        }
    }
}
